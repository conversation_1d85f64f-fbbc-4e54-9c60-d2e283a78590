<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../template/header.php';

// <PERSON>ya admin yang dapat mengakses halaman ini
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Pemeliharaan Database</h2>
        <a href="/absen/" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
        </a>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Pemeliharaan Database</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-download fa-3x mb-3 text-primary"></i>
                                    <h5 class="card-title">Backup Database</h5>
                                    <p class="card-text">Buat cadangan database untuk mencegah kehilangan data.</p>
                                    <a href="backup.php" class="btn btn-primary">
                                        <i class="fas fa-arrow-right"></i> Mulai Backup
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-undo fa-3x mb-3 text-warning"></i>
                                    <h5 class="card-title">Pulihkan Database</h5>
                                    <p class="card-text">Pulihkan database dari file backup yang tersedia.</p>
                                    <div class="btn-group-vertical w-100">
                                        <a href="restore.php" class="btn btn-warning mb-2">
                                            <i class="fas fa-desktop"></i> Restore (Desktop)
                                        </a>
                                        <a href="restore_server_compatible.php" class="btn btn-outline-warning">
                                            <i class="fas fa-server"></i> Restore (Server)
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-broom fa-3x mb-3 text-danger"></i>
                                    <h5 class="card-title">Bersihkan Database</h5>
                                    <p class="card-text">Bersihkan data-data yang sudah tidak diperlukan.</p>
                                    <a href="clean.php" class="btn btn-danger">
                                        <i class="fas fa-arrow-right"></i> Mulai Pembersihan
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Tools Row -->
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-check-circle fa-3x mb-3 text-info"></i>
                                    <h5 class="card-title">Validasi Backup</h5>
                                    <p class="card-text">Validasi file backup sebelum restore untuk mencegah error.</p>
                                    <a href="validate_backup.php" class="btn btn-info">
                                        <i class="fas fa-arrow-right"></i> Validasi File
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-tools fa-3x mb-3 text-warning"></i>
                                    <h5 class="card-title">Perbaiki Backup</h5>
                                    <p class="card-text">Perbaiki file backup lama yang memiliki masalah view.</p>
                                    <a href="fix_backup_files.php" class="btn btn-warning">
                                        <i class="fas fa-arrow-right"></i> Perbaiki File
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-database fa-3x mb-3 text-secondary"></i>
                                    <h5 class="card-title">Migrasi Database</h5>
                                    <p class="card-text">Jalankan script migrasi untuk perbaikan struktur database.</p>
                                    <a href="#" class="btn btn-secondary" onclick="runMigration()">
                                        <i class="fas fa-arrow-right"></i> Jalankan Migrasi
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Data Issues Row -->
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-exclamation-triangle fa-3x mb-3 text-danger"></i>
                                    <h5 class="card-title">Perbaiki Data Truncation</h5>
                                    <p class="card-text">Perbaiki error data truncation pada file backup.</p>
                                    <a href="fix_data_truncation.php" class="btn btn-danger">
                                        <i class="fas fa-arrow-right"></i> Perbaiki Data
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-info-circle"></i> Panduan Troubleshooting</h6>
                                <p><strong>Jika mengalami error saat restore:</strong></p>
                                <ol class="mb-0">
                                    <li><strong>Error "exec() undefined":</strong> Gunakan "Restore (Server)" untuk hosting</li>
                                    <li><strong>Error "Data truncated":</strong> Gunakan tool "Perbaiki Data Truncation"</li>
                                    <li><strong>Error "Table exists":</strong> Gunakan tool "Validasi Backup" dan "Perbaiki Backup"</li>
                                    <li><strong>Timeout error:</strong> Gunakan file backup yang lebih kecil atau server compatible version</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Panduan Penggunaan -->
    <div class="card mt-4">
        <div class="card-body">
            <h5 class="card-title">
                <i class="fas fa-info-circle"></i> Panduan Penggunaan
            </h5>
            <div class="alert alert-info">
                <h6><i class="fas fa-database"></i> Backup Database</h6>
                <ul>
                    <li>Backup akan disimpan di folder <code>database/backups</code></li>
                    <li>Format nama file: <code>backup_YYYY-MM-DD_HH-mm-ss.sql</code></li>
                    <li>Backup mencakup struktur dan data dari semua tabel</li>
                </ul>
            </div>
            <div class="alert alert-warning">
                <h6><i class="fas fa-undo"></i> Pulihkan Database</h6>
                <ul>
                    <li>Pilih file backup (.sql) untuk memulihkan database</li>
                    <li>Proses ini akan menimpa data yang ada saat ini</li>
                    <li>Pastikan file backup valid dan tidak rusak</li>
                </ul>
            </div>
            <div class="alert alert-danger">
                <h6><i class="fas fa-broom"></i> Bersihkan Database</h6>
                <ul>
                    <li>Menghapus semua data kecuali akun admin</li>
                    <li>Tindakan ini tidak dapat dibatalkan</li>
                    <li>Selalu buat backup sebelum membersihkan database</li>
                </ul>
            </div>
            <div class="alert alert-success">
                <h6><i class="fas fa-tools"></i> Tools Tambahan</h6>
                <ul>
                    <li><strong>Validasi Backup:</strong> Periksa file backup sebelum restore untuk mencegah error</li>
                    <li><strong>Perbaiki Backup:</strong> Perbaiki file backup lama yang memiliki masalah dengan view handling</li>
                    <li><strong>Migrasi Database:</strong> Jalankan script migrasi untuk memperbaiki struktur database</li>
                    <li><strong>Perbaiki Data Truncation:</strong> Atasi error data truncation pada kolom status</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function runMigration() {
    if (confirm('Apakah Anda yakin ingin menjalankan script migrasi database? Ini akan membuat stored procedures untuk menangani konflik restore.')) {
        // Create a form to submit the migration request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'run_migration.php';
        form.style.display = 'none';

        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'run_migration';
        input.value = '1';

        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
